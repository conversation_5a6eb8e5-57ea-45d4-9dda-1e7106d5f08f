# Enhanced ElevenLabs Webhook Processing

## Overview

This document describes the enhanced webhook processing system implemented in Phase 5C of the CallSaver.app modernization. The system provides comprehensive handling of ElevenLabs Conversational AI webhook events with improved logging, analytics, and user notifications.

## Supported Webhook Events

### Core Conversation Events

#### 1. `post_call_transcription`
- **Description**: Comprehensive call analysis after conversation completion
- **Data**: Full transcript, analysis results, metadata, cost information
- **Processing**: Updates call record, creates analytics, sends user notification
- **Database Impact**: Updates `calls` table, creates `conversation_analytics` record

#### 2. `post_call_audio`
- **Description**: Audio recording available after call completion
- **Data**: Base64-encoded MP3 audio data
- **Processing**: Stores audio reference, notifies user of availability
- **Database Impact**: Updates `calls.recording_url` field

#### 3. `conversation.started`
- **Description**: Real-time notification when conversation begins
- **Data**: Conversation ID, agent ID, phone numbers
- **Processing**: Creates initial call record, sends start notification
- **Database Impact**: Inserts new record in `calls` table

#### 4. `conversation.ended`
- **Description**: Real-time notification when conversation ends
- **Data**: Basic completion info, duration, summary
- **Processing**: Updates call status, sends completion notification
- **Database Impact**: Updates `calls` table with completion data

### Agent Management Events

#### 5. `agent.updated`
- **Description**: Agent configuration changes in ElevenLabs
- **Data**: Agent ID and updated configuration
- **Processing**: Syncs local database with ElevenLabs changes
- **Database Impact**: Updates `ai_agents` table

### Voice Management Events

#### 6. `voice_removal_notice`
- **Description**: Warning that a shared voice will be removed
- **Data**: Voice ID, removal date
- **Processing**: Identifies affected agents, notifies users
- **Database Impact**: No direct changes, triggers notifications

#### 7. `voice_removal_notice_withdrawn`
- **Description**: Cancellation of voice removal notice
- **Data**: Voice ID
- **Processing**: Notifies users that removal is cancelled
- **Database Impact**: No direct changes, triggers notifications

#### 8. `voice_removed`
- **Description**: Confirmation that voice has been removed
- **Data**: Voice ID
- **Processing**: Updates affected agents, notifies users
- **Database Impact**: Updates `ai_agents` to remove voice references

## Enhanced Features

### 1. Comprehensive Logging
- All webhook events logged to `webhook_logs` table
- Includes payload, processing status, and error messages
- Enables debugging and analytics

### 2. Conversation Analytics
- Automatic creation of analytics records for completed calls
- Extracts metrics from ElevenLabs data:
  - Response times
  - Message counts
  - Call success indicators
  - Cost tracking
- Stores insights and satisfaction scores

### 3. User Notifications
- Real-time notifications for conversation events
- Customized messages based on event type
- Prepared for integration with:
  - WebSocket connections
  - Email notifications
  - Push notifications
  - In-app notification system

### 4. Enhanced Security
- Proper HMAC signature validation
- Timestamp verification (30-minute tolerance)
- Support for ElevenLabs signature format: `t=timestamp,v0=hash`
- Comprehensive error logging

## Database Schema Impact

### Updated Tables

#### `calls` Table Enhancements
```sql
-- Enhanced with comprehensive AI analysis
ai_analysis JSONB -- Now includes:
  - conversation_started_at
  - conversation_ended_at
  - elevenlabs_data (full webhook payload)
  - call_successful
  - evaluation_criteria_results
  - data_collection_results
  - cost
  - termination_reason
  - feedback
```

#### `conversation_analytics` Table
```sql
-- New analytics records created automatically
metrics JSONB -- Includes:
  - call_duration_secs
  - cost
  - transcript_length
  - agent_messages
  - user_messages
  - average_response_time
  - interruptions

insights JSONB -- Includes:
  - call_successful
  - transcript_summary
  - termination_reason
  - evaluation_results
  - data_collection_results
  - feedback
```

#### `webhook_logs` Table
```sql
-- Comprehensive webhook event logging
source VARCHAR -- 'elevenlabs'
event_type VARCHAR -- Event type from webhook
payload JSONB -- Full webhook payload
processed_at TIMESTAMP -- Processing completion time
error_message TEXT -- Error details if processing failed
retry_count INTEGER -- Number of retry attempts
```

## API Endpoints

### Webhook Endpoint
```
POST /api/webhooks/elevenlabs
```

**Headers Required:**
- `elevenlabs-signature`: HMAC signature in format `t=timestamp,v0=hash`
- `Content-Type`: `application/json`

**Request Body:**
```json
{
  "type": "post_call_transcription",
  "event_timestamp": **********,
  "data": {
    // Event-specific data
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "event_type": "post_call_transcription",
  "timestamp": **********
}
```

### Health Check
```
GET /api/webhooks/health
```

Returns status of all webhook endpoints.

## Configuration

### Environment Variables
```bash
# Required
ELEVENLABS_API_KEY=your_api_key_here
ELEVENLABS_WEBHOOK_SECRET=your_webhook_secret_here

# Optional
ELEVENLABS_BASE_URL=https://api.elevenlabs.io/v1
NODE_ENV=development|production
```

### ElevenLabs Dashboard Setup
1. Go to ElevenLabs Conversational AI settings
2. Configure webhook URL: `https://your-domain.com/api/webhooks/elevenlabs`
3. Enable desired event types
4. Copy webhook secret to environment variables

## Testing

### Manual Testing
Use the provided test script:
```bash
cd backend
node test/webhook-test.js
```

### Test Events Included
- Post-call transcription with full analysis
- Conversation start/end events
- Voice management events
- Error scenarios

### Webhook Signature Testing
The test script includes proper HMAC signature generation for testing authentication.

## Monitoring and Debugging

### Logs to Monitor
- `[ElevenLabs]` prefixed logs for service operations
- `[Webhook]` prefixed logs for webhook processing
- Database operation logs from Supabase client

### Common Issues
1. **Invalid Signature**: Check webhook secret configuration
2. **Timestamp Too Old**: Ensure server time is synchronized
3. **Missing Event Type**: Verify webhook payload format
4. **Database Errors**: Check Supabase connection and RLS policies

### Performance Considerations
- Webhook processing is asynchronous
- Database operations use connection pooling
- Error handling prevents webhook failures from breaking the system
- Comprehensive logging enables performance analysis

## Future Enhancements

### Planned Features
1. **Real-time WebSocket Notifications**: Push events to connected clients
2. **Email/SMS Notifications**: Configurable user preferences
3. **Webhook Retry Logic**: Automatic retry for failed processing
4. **Advanced Analytics**: Machine learning insights from conversation data
5. **Audio Storage Integration**: Direct storage of call recordings
6. **Custom Event Handlers**: User-defined webhook processing rules

### Integration Opportunities
- CRM systems (Salesforce, HubSpot)
- Communication platforms (Slack, Teams)
- Analytics platforms (Google Analytics, Mixpanel)
- Customer support systems (Zendesk, Intercom)
