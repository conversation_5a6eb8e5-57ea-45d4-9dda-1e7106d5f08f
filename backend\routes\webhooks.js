const express = require('express');
const router = express.Router();
const webhookController = require('../controllers/webhookController');

// Webhook health check (public endpoint)
router.get('/health', webhookController.webhookHealthCheck);

// ElevenLabs webhook endpoint (public but secured with signature validation)
router.post('/elevenlabs', webhookController.handleElevenLabsWebhook);

// Test endpoint for ElevenLabs webhook (development only)
if (process.env.NODE_ENV === 'development') {
  console.log('[Routes] Registering test endpoint: /elevenlabs-test');
  router.post('/elevenlabs-test', webhookController.handleElevenLabsWebhookTest);
} else {
  console.log('[Routes] Not registering test endpoint - NODE_ENV:', process.env.NODE_ENV);
}

// Twilio webhook endpoints (public but secured with Twilio validation)
router.post('/twilio/voice', webhookController.handleTwilioVoiceWebhook);
router.post('/twilio/sms', webhookController.handleTwilioSmsWebhook);

module.exports = router;
