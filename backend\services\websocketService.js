/**
 * WebSocket Service for CallSaver Backend
 * Phase 5D: Frontend Integration & Real-time Updates
 *
 * Manages WebSocket connections and real-time event broadcasting
 * Integrates with ElevenLabs webhook processing for live updates
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { supabase } = require('../config/supabase');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // userId -> Set of WebSocket connections
    this.connectionCount = 0;
  }

  /**
   * Initialize WebSocket server
   */
  initialize(server) {
    console.log('[WebSocket] Initializing WebSocket server...');
    
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.wss.on('error', this.handleError.bind(this));

    console.log('[WebSocket] WebSocket server initialized on /ws');
  }

  /**
   * Verify client connection (authentication)
   */
  async verifyClient(info) {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');
      const userId = url.searchParams.get('userId');

      if (!token && !userId) {
        console.log('[WebSocket] Connection rejected: Missing authentication');
        return false;
      }

      // If token is provided, verify it
      if (token) {
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          info.req.userId = decoded.userId || decoded.id;
          return true;
        } catch (error) {
          console.log('[WebSocket] Connection rejected: Invalid token');
          return false;
        }
      }

      // If userId is provided (for development/testing), allow connection
      if (userId) {
        info.req.userId = userId;
        return true;
      }

      return false;
    } catch (error) {
      console.error('[WebSocket] Error verifying client:', error);
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(ws, req) {
    const userId = req.userId;
    this.connectionCount++;

    console.log(`[WebSocket] New connection for user ${userId} (total: ${this.connectionCount})`);

    // Add client to user's connection set
    if (!this.clients.has(userId)) {
      this.clients.set(userId, new Set());
    }
    this.clients.get(userId).add(ws);

    // Set up connection handlers
    ws.userId = userId;
    ws.isAlive = true;
    ws.lastPong = Date.now();

    // Handle incoming messages
    ws.on('message', (data) => this.handleMessage(ws, data));
    
    // Handle connection close
    ws.on('close', () => this.handleDisconnection(ws));
    
    // Handle connection errors
    ws.on('error', (error) => this.handleConnectionError(ws, error));

    // Handle pong responses for heartbeat
    ws.on('pong', () => {
      ws.isAlive = true;
      ws.lastPong = Date.now();
    });

    // Send welcome message
    this.sendToClient(ws, {
      type: 'connection_established',
      userId: userId,
      timestamp: new Date().toISOString(),
      message: 'WebSocket connection established'
    });

    // Start heartbeat for this connection
    this.startHeartbeat(ws);
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(ws, data) {
    try {
      const message = JSON.parse(data.toString());
      console.log(`[WebSocket] Message from user ${ws.userId}:`, message.type);

      switch (message.type) {
        case 'ping':
          this.sendToClient(ws, { type: 'pong', timestamp: new Date().toISOString() });
          break;
        
        case 'auth':
          // Re-authentication if needed
          this.handleReauth(ws, message);
          break;
        
        case 'subscribe':
          // Subscribe to specific event types
          this.handleSubscription(ws, message);
          break;
        
        default:
          console.log(`[WebSocket] Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error('[WebSocket] Error parsing message:', error);
      this.sendToClient(ws, {
        type: 'error',
        message: 'Invalid message format',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Handle client disconnection
   */
  handleDisconnection(ws) {
    const userId = ws.userId;
    this.connectionCount--;

    console.log(`[WebSocket] User ${userId} disconnected (total: ${this.connectionCount})`);

    // Remove client from user's connection set
    if (this.clients.has(userId)) {
      this.clients.get(userId).delete(ws);
      
      // Remove user entry if no more connections
      if (this.clients.get(userId).size === 0) {
        this.clients.delete(userId);
      }
    }

    // Clear heartbeat interval
    if (ws.heartbeatInterval) {
      clearInterval(ws.heartbeatInterval);
    }
  }

  /**
   * Handle connection errors
   */
  handleConnectionError(ws, error) {
    console.error(`[WebSocket] Connection error for user ${ws.userId}:`, error);
  }

  /**
   * Handle WebSocket server errors
   */
  handleError(error) {
    console.error('[WebSocket] Server error:', error);
  }

  /**
   * Start heartbeat for connection health monitoring
   */
  startHeartbeat(ws) {
    ws.heartbeatInterval = setInterval(() => {
      if (!ws.isAlive) {
        console.log(`[WebSocket] Terminating dead connection for user ${ws.userId}`);
        ws.terminate();
        return;
      }

      ws.isAlive = false;
      ws.ping();
    }, 30000); // 30 seconds
  }

  /**
   * Handle re-authentication
   */
  handleReauth(ws, message) {
    // Implementation for re-authentication if needed
    console.log(`[WebSocket] Re-auth request from user ${ws.userId}`);
    this.sendToClient(ws, {
      type: 'auth_confirmed',
      userId: ws.userId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle event subscriptions
   */
  handleSubscription(ws, message) {
    const { events } = message;
    ws.subscribedEvents = new Set(events || []);
    
    console.log(`[WebSocket] User ${ws.userId} subscribed to events:`, Array.from(ws.subscribedEvents));
    
    this.sendToClient(ws, {
      type: 'subscription_confirmed',
      events: Array.from(ws.subscribedEvents),
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send message to specific client
   */
  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * Broadcast message to all connections of a specific user
   */
  broadcastToUser(userId, message) {
    const userConnections = this.clients.get(userId);
    if (!userConnections || userConnections.size === 0) {
      console.log(`[WebSocket] No active connections for user ${userId}`);
      return false;
    }

    let sentCount = 0;
    userConnections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        // Check if client is subscribed to this event type
        if (!ws.subscribedEvents || ws.subscribedEvents.has(message.type) || ws.subscribedEvents.has('all')) {
          this.sendToClient(ws, message);
          sentCount++;
        }
      }
    });

    console.log(`[WebSocket] Broadcasted ${message.type} to ${sentCount} connections for user ${userId}`);
    return sentCount > 0;
  }

  /**
   * Broadcast message to all connected clients
   */
  broadcastToAll(message) {
    let sentCount = 0;
    this.clients.forEach((connections, userId) => {
      connections.forEach(ws => {
        if (ws.readyState === WebSocket.OPEN) {
          if (!ws.subscribedEvents || ws.subscribedEvents.has(message.type) || ws.subscribedEvents.has('all')) {
            this.sendToClient(ws, message);
            sentCount++;
          }
        }
      });
    });

    console.log(`[WebSocket] Broadcasted ${message.type} to ${sentCount} total connections`);
    return sentCount > 0;
  }

  /**
   * Get connection statistics
   */
  getStats() {
    return {
      totalConnections: this.connectionCount,
      uniqueUsers: this.clients.size,
      userConnections: Array.from(this.clients.entries()).map(([userId, connections]) => ({
        userId,
        connectionCount: connections.size
      }))
    };
  }

  /**
   * Graceful shutdown
   */
  shutdown() {
    console.log('[WebSocket] Shutting down WebSocket server...');
    
    if (this.wss) {
      this.wss.clients.forEach(ws => {
        ws.terminate();
      });
      this.wss.close();
    }
    
    this.clients.clear();
    this.connectionCount = 0;
    
    console.log('[WebSocket] WebSocket server shutdown complete');
  }
}

// Export singleton instance
const websocketService = new WebSocketService();
module.exports = websocketService;
