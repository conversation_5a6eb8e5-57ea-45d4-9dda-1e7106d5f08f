/**
 * Webhook Controller for CallSaver Backend
 * Phase 5A: ElevenLabs Conversational AI Integration
 *
 * Handles webhook events from ElevenLabs and other external services
 */

const elevenLabsService = require('../services/elevenLabsService');
const { supabase, executeWithErrorHandling } = require('../config/supabase');
const crypto = require('crypto');

/**
 * @desc    Handle ElevenLabs webhook events
 * @route   POST /api/webhooks/elevenlabs
 * @access  Public (but secured with signature validation)
 */
const handleElevenLabsWebhook = async (req, res) => {
  try {
    const signature = req.headers['elevenlabs-signature'] || req.headers['x-elevenlabs-signature'] || req.headers['x-signature'];
    const payload = JSON.stringify(req.body);

    console.log('[Webhook] Received ElevenLabs webhook event');
    console.log('[Webhook] Event type:', req.body.type || req.body.event_type);
    console.log('[Webhook] Headers:', Object.keys(req.headers));

    // Validate webhook signature for security (skip in development/testing)
    const skipSignatureValidation = process.env.NODE_ENV === 'development' && signature === 'test-signature';

    if (!skipSignatureValidation && !elevenLabsService.validateWebhookSignature(payload, signature)) {
      console.error('[Webhook] Invalid signature for ElevenLabs webhook');
      console.error('[Webhook] Expected signature format: t=timestamp,v0=hash');
      console.error('[Webhook] Received signature:', signature);

      return res.status(401).json({
        success: false,
        message: 'Invalid webhook signature',
        code: 'INVALID_SIGNATURE'
      });
    }

    if (skipSignatureValidation) {
      console.log('[Webhook] Skipping signature validation for development testing');
    }

    // Extract event type and data (handle both formats)
    const { event_type, type, data, event_timestamp } = req.body;
    const finalEventType = event_type || type;

    if (!finalEventType) {
      console.error('[Webhook] Missing event type in webhook payload');
      return res.status(400).json({
        success: false,
        message: 'Missing event type in webhook payload',
        code: 'MISSING_EVENT_TYPE'
      });
    }

    console.log(`[Webhook] Processing ElevenLabs event: ${finalEventType}`);
    console.log(`[Webhook] Event timestamp: ${event_timestamp}`);

    // Add timestamp to event data for processing
    const enrichedData = {
      ...data,
      event_timestamp,
      webhook_received_at: new Date().toISOString()
    };

    // Process the webhook event
    const result = await elevenLabsService.processWebhookEvent(finalEventType, enrichedData);

    if (result.success) {
      console.log(`[Webhook] Successfully processed event: ${finalEventType}`);
      res.status(200).json({
        success: true,
        message: 'Webhook processed successfully',
        event_type: finalEventType,
        timestamp: event_timestamp
      });
    } else {
      console.error(`[Webhook] Failed to process event ${finalEventType}:`, result.error);
      res.status(500).json({
        success: false,
        message: 'Failed to process webhook event',
        error: result.error,
        event_type: finalEventType
      });
    }

  } catch (error) {
    console.error('[Webhook] Error processing ElevenLabs webhook:', error);
    console.error('[Webhook] Request body:', req.body);
    console.error('[Webhook] Request headers:', req.headers);

    res.status(500).json({
      success: false,
      message: 'Internal server error processing webhook',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Test handler for ElevenLabs webhook (development only - bypasses signature validation)
const handleElevenLabsWebhookTest = async (req, res) => {
  if (process.env.NODE_ENV !== 'development') {
    return res.status(404).json({ success: false, message: 'Not found' });
  }

  try {
    console.log('[Webhook] Received ElevenLabs TEST webhook event');

    // Extract event type and data (handle both formats like the main handler)
    const { event_type, type, data, event_timestamp } = req.body;
    const finalEventType = event_type || type;

    console.log('[Webhook] Event type:', finalEventType);
    console.log('[Webhook] Bypassing signature validation for test');

    if (!finalEventType) {
      console.error('[Webhook] Missing event type in TEST webhook payload');
      return res.status(400).json({
        success: false,
        message: 'Missing event type in webhook payload',
        code: 'MISSING_EVENT_TYPE'
      });
    }

    const finalEventTimestamp = event_timestamp || new Date().toISOString();

    // Enrich the data with additional context
    const enrichedData = {
      ...data,
      event_timestamp: finalEventTimestamp,
      webhook_received_at: new Date().toISOString()
    };

    // Process the webhook event
    const result = await elevenLabsService.processWebhookEvent(finalEventType, enrichedData);

    if (result.success) {
      console.log(`[Webhook] Successfully processed TEST event: ${finalEventType}`);
      res.status(200).json({
        success: true,
        message: 'Test webhook processed successfully',
        event_type: finalEventType,
        timestamp: event_timestamp
      });
    } else {
      console.error(`[Webhook] Failed to process TEST event ${finalEventType}:`, result.error);
      res.status(500).json({
        success: false,
        message: 'Failed to process test webhook event',
        error: result.error,
        event_type: finalEventType
      });
    }

  } catch (error) {
    console.error('[Webhook] Error processing ElevenLabs TEST webhook:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
};

/**
 * @desc    Handle Twilio webhook events - Route calls to ElevenLabs agents
 * @route   POST /api/webhooks/twilio/voice
 * @access  Public (but secured with Twilio validation)
 */
const handleTwilioVoiceWebhook = async (req, res) => {
  try {
    console.log('[Webhook] Received Twilio voice webhook event');
    console.log('[Webhook] Twilio payload:', req.body);

    const { CallSid, From, To, CallStatus } = req.body;

    if (!CallSid || !From || !To) {
      console.error('[Webhook] Missing required Twilio parameters');
      return res.status(400).send('<?xml version="1.0" encoding="UTF-8"?><Response><Say>Error processing call</Say></Response>');
    }

    // Handle different call statuses
    if (CallStatus === 'ringing' || CallStatus === 'in-progress') {
      // Find the agent assigned to this phone number
      const agentResult = await findAgentForPhoneNumber(To);

      if (!agentResult.success) {
        console.error(`[Webhook] No agent found for phone number ${To}`);
        return res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
          <Response>
            <Say voice="alice">Sorry, no AI assistant is configured for this number. Please contact support.</Say>
          </Response>`);
      }

      console.log(`[Webhook] Routing call to ElevenLabs agent: ${agentResult.agent.elevenlabs_agent_id}`);

      // For ElevenLabs integration, we need to redirect the call to ElevenLabs
      // ElevenLabs handles the conversation automatically once the number is imported
      // This webhook is mainly for logging and fallback handling

      // Log the call in our database
      await logIncomingCall({
        callSid: CallSid,
        fromNumber: From,
        toNumber: To,
        agentId: agentResult.agent.id,
        userId: agentResult.agent.user_id,
        phoneNumberId: agentResult.phoneNumberId
      });

      // Return TwiML to continue the call (ElevenLabs should handle this automatically)
      return res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
        <Response>
          <Say voice="alice">Connecting you to our AI assistant. Please hold.</Say>
        </Response>`);
    }

    // For other call statuses (completed, failed, etc.), just log
    console.log(`[Webhook] Call ${CallSid} status: ${CallStatus}`);

    res.status(200).send('<?xml version="1.0" encoding="UTF-8"?><Response></Response>');

  } catch (error) {
    console.error('[Webhook] Error processing Twilio voice webhook:', error);
    res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
      <Response>
        <Say voice="alice">We're experiencing technical difficulties. Please try again later.</Say>
      </Response>`);
  }
};

/**
 * @desc    Handle Twilio SMS webhook events (existing functionality)
 * @route   POST /api/webhooks/twilio/sms
 * @access  Public (but secured with Twilio validation)
 */
const handleTwilioSmsWebhook = async (req, res) => {
  try {
    console.log('[Webhook] Received Twilio SMS webhook event');
    
    // TODO: Implement Twilio SMS webhook processing
    // This could integrate with ElevenLabs for AI text responses
    
    res.status(200).json({
      success: true,
      message: 'Twilio SMS webhook received'
    });
    
  } catch (error) {
    console.error('[Webhook] Error processing Twilio SMS webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error processing webhook',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Webhook health check
 * @route   GET /api/webhooks/health
 * @access  Public
 */
const webhookHealthCheck = async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Webhook endpoints are healthy',
      timestamp: new Date().toISOString(),
      endpoints: [
        '/api/webhooks/elevenlabs',
        '/api/webhooks/twilio/voice',
        '/api/webhooks/twilio/sms'
      ]
    });
  } catch (error) {
    console.error('[Webhook] Health check error:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook health check failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Find the AI agent assigned to a phone number
 */
async function findAgentForPhoneNumber(phoneNumber) {
  try {
    const result = await executeWithErrorHandling(
      () => supabase
        .from('phone_number_agents')
        .select(`
          id,
          phone_number_id,
          phone_numbers!inner (
            id,
            user_id,
            number
          ),
          ai_agents!inner (
            id,
            user_id,
            name,
            elevenlabs_agent_id,
            is_active
          )
        `)
        .eq('phone_numbers.number', phoneNumber)
        .eq('is_active', true)
        .eq('ai_agents.is_active', true)
        .single(),
      `Find agent for phone number ${phoneNumber}`
    );

    if (!result.success || !result.data) {
      return {
        success: false,
        error: 'No active agent found for this phone number'
      };
    }

    return {
      success: true,
      agent: result.data.ai_agents,
      phoneNumberId: result.data.phone_numbers.id
    };
  } catch (error) {
    console.error('[Webhook] Error finding agent for phone number:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Log incoming call to database
 */
async function logIncomingCall(callData) {
  try {
    const result = await executeWithErrorHandling(
      () => supabase
        .from('calls')
        .insert({
          user_id: callData.userId,
          phone_number_id: callData.phoneNumberId,
          external_id: callData.callSid,
          from_number: callData.fromNumber,
          to_number: callData.toNumber,
          direction: 'inbound',
          status: 'in-progress',
          ai_analysis: {
            agent_id: callData.agentId,
            twilio_call_sid: callData.callSid,
            call_started_at: new Date().toISOString()
          }
        })
        .select()
        .single(),
      `Log incoming call ${callData.callSid}`
    );

    if (result.success) {
      console.log(`[Webhook] Call logged: ${callData.callSid}`);
    } else {
      console.error(`[Webhook] Failed to log call: ${result.error}`);
    }

    return result;
  } catch (error) {
    console.error('[Webhook] Error logging call:', error);
    return { success: false, error: error.message };
  }
}

module.exports = {
  handleElevenLabsWebhook,
  handleElevenLabsWebhookTest,
  handleTwilioVoiceWebhook,
  handleTwilioSmsWebhook,
  webhookHealthCheck,
};
