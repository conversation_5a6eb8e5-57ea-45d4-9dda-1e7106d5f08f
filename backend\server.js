const express = require('express');
const cors = require('cors');
const http = require('http');
const { PrismaClient } = require('@prisma/client');
const { supabase, testConnection } = require('./config/supabase');
const { protect } = require('./middleware/authMiddleware');
const websocketService = require('./services/websocketService');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3006;

// Keep Prisma for schema management (as per Phase 2 strategy)
const prisma = new PrismaClient();

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Health check route
app.get('/', (req, res) => {
  res.send('Callsaver V2 Backend API Running');
});

// API Routes
app.get('/api', (req, res) => {
  res.json({
    message: 'Callsaver V2 API',
    version: '2.0.0',
    endpoints: [
      '/api/calls',
      '/api/numbers',
      '/api/messages',
      '/api/automation',
      '/api/agents',
      '/api/webhooks',
      '/api/phone-numbers',
      '/api/users'
    ]
  });
});

// Import the calls router
const callsRouter = require('./routes/calls');
app.use('/api/calls', callsRouter);

// Import the numbers router
const numbersRouter = require('./routes/numbers');
app.use('/api/numbers', numbersRouter);

// Import the messages router
const messagesRouter = require('./routes/messages');
app.use('/api/messages', messagesRouter);

// Import the automation router
const automationRouter = require('./routes/automation');
app.use('/api/automation', automationRouter);

// Import the agents router
const agentsRouter = require('./routes/agents');
app.use('/api/agents', agentsRouter);

// Import the webhooks router
const webhooksRouter = require('./routes/webhooks');
app.use('/api/webhooks', webhooksRouter);

// Import the phone number agents router
const phoneNumberAgentsRouter = require('./routes/phoneNumberAgents');
app.use('/api/phone-numbers', phoneNumberAgentsRouter);

// Create a users router for user-specific endpoints
const express_router = express.Router();

// Apply authentication middleware to all user routes
express_router.use(protect);

// Get current user information
express_router.get('/me', async (req, res) => {
  try {
    // User information is already attached to req.user by the auth middleware
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Return user data in the format expected by the frontend
    res.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name || user.email?.split('@')[0] || '',
        role: user.role || 'user',
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Get user's phone numbers
express_router.get('/numbers', async (req, res) => {
  try {
    // In a real implementation, you would extract the user ID from the JWT token
    // For now, we'll return a mock response
    res.json({
      success: true,
      count: 2,
      data: [
        {
          id: "1",
          number: "+14155552671",
          friendlyName: "(*************",
          countryCode: "US",
          region: "California",
          locality: "San Francisco",
          isActive: true,
          isPrimary: true,
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
          capabilities: {
            voice: true,
            sms: true,
            fax: false
          }
        },
        {
          id: "2",
          number: "+14255551234",
          friendlyName: "(*************",
          countryCode: "US",
          region: "Washington",
          locality: "Seattle",
          isActive: true,
          isPrimary: false,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          capabilities: {
            voice: true,
            sms: true,
            fax: false
          }
        }
      ]
    });
  } catch (error) {
    console.error('Error fetching user numbers:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
});

// Mount the users router
app.use('/api/users', express_router);

// WebSocket status endpoint
app.get('/api/websocket/status', (req, res) => {
  const stats = websocketService.getStats();
  res.json({
    success: true,
    data: {
      ...stats,
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Initialize server with database connection test
const startServer = async () => {
  try {
    // Test Supabase connection
    console.log('🔄 Testing database connections...');
    const supabaseConnected = await testConnection();

    if (!supabaseConnected) {
      console.error('❌ Supabase connection failed');
      process.exit(1);
    }

    // Initialize WebSocket service
    console.log('🔄 Initializing WebSocket service...');
    websocketService.initialize(server);

    // Start server
    server.listen(PORT, () => {
      console.log(`🚀 CallSaver Backend Server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
      console.log(`✅ Supabase connected: ${supabase.supabaseUrl}`);
      console.log(`🔌 WebSocket server running on ws://localhost:${PORT}/ws`);
    });
  } catch (error) {
    console.error('❌ Server startup failed:', error);
    process.exit(1);
  }
};

// Export app for testing
module.exports = app;

// Graceful shutdown handling
const gracefulShutdown = (signal) => {
  console.log(`\n🔄 Received ${signal}. Starting graceful shutdown...`);

  websocketService.shutdown();

  server.close(() => {
    console.log('✅ HTTP server closed');
    process.exit(0);
  });

  // Force close after 10 seconds
  setTimeout(() => {
    console.log('⚠️ Forcing shutdown after timeout');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start the server only if this file is run directly
if (require.main === module) {
  startServer();
}