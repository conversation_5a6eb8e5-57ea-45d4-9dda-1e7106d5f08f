/**
 * ElevenLabs Webhook Testing Script
 * Phase 5C: Enhanced Webhook Processing Test
 * 
 * This script tests the enhanced ElevenLabs webhook processing functionality
 */

const axios = require('axios');
const crypto = require('crypto');

// Test configuration
const WEBHOOK_URL = 'http://localhost:3006/api/webhooks/elevenlabs';
const WEBHOOK_SECRET = process.env.ELEVENLABS_WEBHOOK_SECRET || 'test-secret-key';

/**
 * Create HMAC signature for ElevenLabs webhook
 */
function createWebhookSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadToSign = `${timestamp}.${payload}`;
  
  const hash = crypto
    .createHmac('sha256', secret)
    .update(payloadToSign)
    .digest('hex');
  
  return `t=${timestamp},v0=${hash}`;
}

/**
 * Test webhook events
 */
const testEvents = [
  {
    name: 'Post-call Transcription',
    payload: {
      type: 'post_call_transcription',
      event_timestamp: Math.floor(Date.now() / 1000),
      data: {
        agent_id: 'test-agent-123',
        conversation_id: 'conv-test-456',
        status: 'done',
        transcript: [
          {
            role: 'agent',
            message: 'Hello, how can I help you today?',
            time_in_call_secs: 0,
            conversation_turn_metrics: {
              convai_llm_service_ttfb: { elapsed_time: 0.5 }
            }
          },
          {
            role: 'user',
            message: 'I need help with my account',
            time_in_call_secs: 3
          },
          {
            role: 'agent',
            message: 'I can help you with that. Let me look up your account.',
            time_in_call_secs: 5,
            conversation_turn_metrics: {
              convai_llm_service_ttfb: { elapsed_time: 0.3 }
            }
          }
        ],
        metadata: {
          start_time_unix_secs: Math.floor(Date.now() / 1000) - 120,
          call_duration_secs: 120,
          cost: 250,
          termination_reason: 'user_hangup',
          feedback: {
            overall_score: 4,
            likes: 1,
            dislikes: 0
          }
        },
        analysis: {
          call_successful: 'success',
          transcript_summary: 'Customer called for account help. Agent provided assistance and resolved the issue.',
          evaluation_criteria_results: {
            helpfulness: 'high',
            clarity: 'high'
          },
          data_collection_results: {
            customer_satisfaction: 'satisfied'
          }
        }
      }
    }
  },
  {
    name: 'Conversation Started',
    payload: {
      type: 'conversation.started',
      event_timestamp: Math.floor(Date.now() / 1000),
      data: {
        conversation_id: 'conv-started-789',
        agent_id: 'test-agent-123',
        caller_number: '+**********',
        called_number: '+**********'
      }
    }
  },
  {
    name: 'Conversation Ended',
    payload: {
      type: 'conversation.ended',
      event_timestamp: Math.floor(Date.now() / 1000),
      data: {
        conversation_id: 'conv-ended-101',
        duration_seconds: 180,
        transcript: 'Full conversation transcript here...',
        summary: 'Customer inquiry resolved successfully'
      }
    }
  },
  {
    name: 'Voice Removal Notice',
    payload: {
      type: 'voice_removal_notice',
      event_timestamp: Math.floor(Date.now() / 1000),
      data: {
        voice_id: 'voice-123',
        removal_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      }
    }
  }
];

/**
 * Send test webhook
 */
async function sendTestWebhook(testEvent) {
  try {
    const payload = JSON.stringify(testEvent.payload);
    const signature = createWebhookSignature(payload, WEBHOOK_SECRET);
    
    console.log(`\n🧪 Testing: ${testEvent.name}`);
    console.log(`📤 Payload: ${payload.substring(0, 100)}...`);
    console.log(`🔐 Signature: ${signature}`);
    
    const response = await axios.post(WEBHOOK_URL, testEvent.payload, {
      headers: {
        'Content-Type': 'application/json',
        'elevenlabs-signature': signature
      },
      timeout: 10000
    });
    
    console.log(`✅ Success: ${response.status} - ${response.data.message}`);
    return { success: true, response: response.data };
    
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || 'Network'} - ${error.response?.data?.message || error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Run all webhook tests
 */
async function runWebhookTests() {
  console.log('🚀 Starting ElevenLabs Webhook Tests');
  console.log(`📍 Target URL: ${WEBHOOK_URL}`);
  console.log(`🔑 Using webhook secret: ${WEBHOOK_SECRET ? 'Configured' : 'Not configured'}`);
  
  const results = [];
  
  for (const testEvent of testEvents) {
    const result = await sendTestWebhook(testEvent);
    results.push({ name: testEvent.name, ...result });
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
  });
  
  console.log(`\n📈 Total: ${results.length} tests`);
  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All webhook tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runWebhookTests().catch(console.error);
}

module.exports = {
  runWebhookTests,
  sendTestWebhook,
  createWebhookSignature
};
